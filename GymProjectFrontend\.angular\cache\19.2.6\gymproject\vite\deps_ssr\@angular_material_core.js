import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  AnimationCurves,
  AnimationDurations,
  DateAdapter,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
  MAT_DATE_LOCALE_FACTORY,
  MAT_NATIVE_DATE_FORMATS,
  MatLine,
  MatLineModule,
  MatNativeDateModule,
  NativeDateAdapter,
  NativeDateModule,
  VERSION,
  provideNativeDateAdapter,
  setLines
} from "./chunk-MHFGQBWA.js";
import {
  ErrorStateMatcher,
  ShowOnDirtyErrorStateMatcher,
  _ErrorStateTracker
} from "./chunk-RWMTIGMP.js";
import {
  _MatInternalFormField
} from "./chunk-KC6KYB7J.js";
import {
  MAT_OPTGROUP,
  MAT_OPTION_PARENT_COMPONENT,
  MatOptgroup,
  MatOption,
  MatOptionModule,
  MatOptionSelectionChange,
  MatPseudoCheckbox,
  MatPseudoCheckboxModule,
  _countGroupLabelsBeforeOption,
  _getOptionScrollPosition
} from "./chunk-SFQMG6NK.js";
import "./chunk-FTZZESUS.js";
import {
  MatRippleLoader
} from "./chunk-FX4XRALR.js";
import {
  MatRippleModule
} from "./chunk-QSUMQQBJ.js";
import {
  MAT_RIPPLE_GLOBAL_OPTIONS,
  MatRipple,
  RippleRef,
  RippleRenderer,
  RippleState,
  defaultRippleAnimationConfig
} from "./chunk-QEDQNHEU.js";
import {
  _StructuralStylesLoader
} from "./chunk-HGIABHWM.js";
import "./chunk-EA4DKPP3.js";
import "./chunk-V2OSU5GV.js";
import "./chunk-ZWPVRPHO.js";
import {
  MATERIAL_SANITY_CHECKS,
  MatCommonModule
} from "./chunk-L6CMC7DI.js";
import "./chunk-HJTKKQ3X.js";
import "./chunk-WSZNNRDU.js";
import "./chunk-6LMJTVPT.js";
import "./chunk-FTWXCXPA.js";
import "./chunk-ISBA5P47.js";
import "./chunk-EEMKRXTQ.js";
import "./chunk-FBRWNC4B.js";
import "./chunk-NNB67BKT.js";
import "./chunk-HGVHWTGE.js";
import "./chunk-EXQLYBKH.js";
import "./chunk-IUOK4BIQ.js";
import "./chunk-GBTWTWDP.js";
export {
  AnimationCurves,
  AnimationDurations,
  DateAdapter,
  ErrorStateMatcher,
  MATERIAL_SANITY_CHECKS,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
  MAT_DATE_LOCALE_FACTORY,
  MAT_NATIVE_DATE_FORMATS,
  MAT_OPTGROUP,
  MAT_OPTION_PARENT_COMPONENT,
  MAT_RIPPLE_GLOBAL_OPTIONS,
  MatCommonModule,
  MatLine,
  MatLineModule,
  MatNativeDateModule,
  MatOptgroup,
  MatOption,
  MatOptionModule,
  MatOptionSelectionChange,
  MatPseudoCheckbox,
  MatPseudoCheckboxModule,
  MatRipple,
  MatRippleLoader,
  MatRippleModule,
  NativeDateAdapter,
  NativeDateModule,
  RippleRef,
  RippleRenderer,
  RippleState,
  ShowOnDirtyErrorStateMatcher,
  VERSION,
  _ErrorStateTracker,
  _MatInternalFormField,
  _StructuralStylesLoader,
  _countGroupLabelsBeforeOption,
  _getOptionScrollPosition,
  defaultRippleAnimationConfig,
  provideNativeDateAdapter,
  setLines
};
