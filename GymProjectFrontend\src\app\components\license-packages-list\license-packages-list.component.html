<div class="modern-table-container">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <!-- Empty State -->
  <div *ngIf="!isLoading && licensePackages.length === 0" class="empty-state">
    <div class="empty-state-icon">
      <i class="fas fa-box-open"></i>
    </div>
    <h5 class="empty-state-title">Henüz lisans paketi bulunmamaktadır</h5>
    <p class="empty-state-text">İlk lisans paketinizi oluşturmak için yukarıdaki formu kullanın.</p>
  </div>

  <!-- Data Table -->
  <div *ngIf="!isLoading && licensePackages.length > 0" class="modern-table-wrapper">
    <div class="table-responsive">
      <table class="modern-table">
        <thead>
          <tr>
            <th><PERSON>et Adı</th>
            <th>A<PERSON><PERSON>klama</th>
            <th>Rol</th>
            <th>Süre</th>
            <th>Fiyat</th>
            <th>Durum</th>
            <th class="text-center">İşlemler</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of licensePackages; trackBy: trackByPackageId" class="table-row">
            <td class="package-name">
              <div class="package-info">
                <span class="package-title">{{ item.name }}</span>
              </div>
            </td>
            <td class="package-description">
              <span class="description-text">{{ item.description }}</span>
            </td>
            <td class="package-role">
              <span class="role-badge" [class]="'role-' + item.role.toLowerCase()">
                {{ item.role | titlecase }}
              </span>
            </td>
            <td class="package-duration">
              <span class="duration-text">{{ formatDuration(item.durationDays) }}</span>
            </td>
            <td class="package-price">
              <span class="price-amount">{{ item.price | currency:'TRY':'symbol':'1.2-2' }}</span>
            </td>
            <td class="package-status">
              <span class="status-badge" [class]="item.isActive ? 'status-active' : 'status-inactive'">
                {{ item.isActive ? 'Aktif' : 'Pasif' }}
              </span>
            </td>
            <td class="package-actions text-center">
              <div class="action-buttons">
                <button
                  class="action-btn edit-btn"
                  (click)="openEditDialog(item)"
                  title="Düzenle"
                >
                  <i class="fas fa-edit"></i>
                </button>
                <button
                  class="action-btn delete-btn"
                  (click)="deleteLicensePackage(item.licensePackageID, item.name)"
                  title="Sil"
                >
                  <i class="fas fa-trash-alt"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
  