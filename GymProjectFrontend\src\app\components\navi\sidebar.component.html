<div class="sidebar" [ngClass]="{'collapsed': collapsed}">
  <!-- Sidebar Header -->
  <div class="sidebar-header">
    <a routerLink="/todayentries" class="logo-link"> <!-- Yönlendirme eklendi -->
      <div class="logo-container">
        <i class="fas fa-dumbbell"></i>
        <span class="logo-text" *ngIf="!collapsed">GymKod Pro</span>
      </div>
    </a>
    <button class="toggle-btn" (click)="onToggleSidebar()" title="Kenar Çubuğunu Daralt/Genişlet">
      <i class="fas" [ngClass]="collapsed ? 'fa-angle-right' : 'fa-angle-left'"></i>
    </button>
  </div>

  <!-- Sidebar Content -->
  <div class="sidebar-content">
    <!-- Member QR Section -->
    <div class="menu-section" *ngIf="isAuthenticated() && isMember && hasMemberQRAccess">
      <a class="menu-item" routerLink="my-qr" routerLinkActive="active">
        <i class="fas fa-qrcode"></i>
        <span *ngIf="!collapsed">QR Kodum</span>
      </a>
    </div>

    <!-- Customer Section -->
    <div class="menu-section" *ngIf="isAuthenticated() && isAdmin">
      <div class="menu-header" (click)="toggleMenuSection('customer')">
        <div class="menu-icon">
          <i class="fas fa-users"></i>
        </div>
        <span class="menu-title" *ngIf="!collapsed">Müşteri</span>
        <i class="fas fa-chevron-down menu-arrow" *ngIf="!collapsed" [ngClass]="{'rotated': customerMenuOpen}"></i>
      </div>

      <div class="menu-items" [ngClass]="{'expanded': customerMenuOpen || collapsed}">
        <a class="menu-item" routerLink="allmembers" routerLinkActive="active">
          <i class="fas fa-list"></i>
          <span *ngIf="!collapsed">Bütün Üyeler</span>
        </a>
        <a class="menu-item" routerLink="memberfilter" routerLinkActive="active">
          <i class="fas fa-user-check"></i>
          <span *ngIf="!collapsed">Aktif Üyeler</span>
        </a>
        <a class="menu-item" routerLink="frozen-memberships" routerLinkActive="active">
          <i class="fas fa-snowflake"></i>
          <span *ngIf="!collapsed">Dondurulmuş Üyelikler</span>
        </a>
        <a class="menu-item" routerLink="memberremainingday" routerLinkActive="active">
          <i class="fas fa-hourglass-end"></i>
          <span *ngIf="!collapsed">Üyelik Bitişi Yaklaşanlar</span>
        </a>
        <a class="menu-item" routerLink="debtormember" routerLinkActive="active">
          <i class="fas fa-hand-holding-usd"></i>
          <span *ngIf="!collapsed">Borçlu Üyeler</span>
        </a>
        <a class="menu-item" routerLink="birthdays" routerLinkActive="active">
          <i class="fas fa-birthday-cake"></i>
          <span *ngIf="!collapsed">Doğum Günleri</span>
        </a>
        <a class="menu-item" routerLink="todayentries" routerLinkActive="active">
          <i class="fas fa-door-open"></i>
          <span *ngIf="!collapsed">Giriş-Çıkış Kayıtları</span>
        </a>
        <a class="menu-item" routerLink="paymenthistory" routerLinkActive="active">
          <i class="fas fa-cash-register"></i>
          <span *ngIf="!collapsed">Kasa Raporu</span>
        </a>
        <a class="menu-item" routerLink="/expenses" routerLinkActive="active"> <!-- Gider Yönetimi Eklendi -->
          <i class="fas fa-file-invoice-dollar"></i>
          <span *ngIf="!collapsed">Gider Yönetimi</span>
        </a>
        <a class="menu-item" routerLink="membershiptype/add" routerLinkActive="active">
          <i class="fas fa-plus-circle"></i>
          <span *ngIf="!collapsed">Üyelik Türü Ekleme</span>
        </a>
        <a class="menu-item" routerLink="member/add" routerLinkActive="active">
          <i class="fas fa-user-plus"></i>
          <span *ngIf="!collapsed">Yeni Üye</span>
        </a>
        <a class="menu-item" routerLink="membership/add" routerLinkActive="active">
          <i class="fas fa-clock"></i>
          <span *ngIf="!collapsed">Üyelik Süresi Ekleme</span>
        </a>
      </div>
    </div>

    <!-- E-Para Section -->
    <div class="menu-section" *ngIf="isAuthenticated() && isAdmin">
      <div class="menu-header" (click)="toggleMenuSection('eMoney')">
        <div class="menu-icon">
          <i class="fas fa-wallet"></i>
        </div>
        <span class="menu-title" *ngIf="!collapsed">E-Para</span>
        <i class="fas fa-chevron-down menu-arrow" *ngIf="!collapsed" [ngClass]="{'rotated': eMoneyMenuOpen}"></i>
      </div>

      <div class="menu-items" [ngClass]="{'expanded': eMoneyMenuOpen || collapsed}">
        <a class="menu-item" routerLink="memberbalancetopup" routerLinkActive="active">
          <i class="fas fa-money-bill-wave"></i>
          <span *ngIf="!collapsed">Bakiye Yükle - Düşür</span>
        </a>
        <a class="menu-item" routerLink="products" routerLinkActive="active">
          <i class="fas fa-shopping-basket"></i>
          <span *ngIf="!collapsed">Ürün Ekle</span>
        </a>
        <a class="menu-item" routerLink="product-sale" routerLinkActive="active">
          <i class="fas fa-shopping-cart"></i>
          <span *ngIf="!collapsed">Ürün Sat</span>
        </a>
        <a class="menu-item" routerLink="transactions" routerLinkActive="active">
          <i class="fas fa-exchange-alt"></i>
          <span *ngIf="!collapsed">İşlem Takibi</span>
        </a>
      </div>
    </div>

    <!-- Training Section -->
    <div class="menu-section" *ngIf="isAuthenticated() && isAdmin">
      <div class="menu-header" (click)="toggleMenuSection('training')">
        <div class="menu-icon">
          <i class="fas fa-dumbbell"></i>
        </div>
        <span class="menu-title" *ngIf="!collapsed">Antrenman</span>
        <i class="fas fa-chevron-down menu-arrow" *ngIf="!collapsed" [ngClass]="{'rotated': trainingMenuOpen}"></i>
      </div>

      <div class="menu-items" [ngClass]="{'expanded': trainingMenuOpen || collapsed}">
        <a class="menu-item" routerLink="/exercises" routerLinkActive="active">
          <i class="fas fa-list-ul"></i>
          <span *ngIf="!collapsed">Egzersiz Listesi</span>
        </a>
        <a class="menu-item" routerLink="/workout-programs" routerLinkActive="active">
          <i class="fas fa-dumbbell"></i>
          <span *ngIf="!collapsed">Antrenman Programları</span>
        </a>
        <a class="menu-item" routerLink="/member-workout-assignments" routerLinkActive="active">
          <i class="fas fa-user-plus"></i>
          <span *ngIf="!collapsed">Program Atamaları</span>
        </a>
        <!-- Gelecekte eklenecek antrenman modülleri için yer -->
        <!--
        <a class="menu-item" routerLink="/workout-templates" routerLinkActive="active">
          <i class="fas fa-file-alt"></i>
          <span *ngIf="!collapsed">Program Şablonları</span>
        </a>
        -->
      </div>
    </div>

    <!-- Owner Dashboard Section -->
    <div class="menu-section" *ngIf="isOwner">
      <a class="menu-item single-item" routerLink="license-dashboard" routerLinkActive="active">
        <i class="fas fa-tachometer-alt"></i>
        <span *ngIf="!collapsed">Ana Panel</span>
      </a>
    </div>

    <!-- Gym Management Section -->
    <div class="menu-section" *ngIf="isOwner">
      <div class="menu-header" (click)="toggleMenuSection('gym')">
        <div class="menu-icon">
          <i class="fas fa-building"></i>
        </div>
        <span class="menu-title" *ngIf="!collapsed">Salon Yönetimi</span>
        <i class="fas fa-chevron-down menu-arrow" *ngIf="!collapsed" [ngClass]="{'rotated': gymMenuOpen}"></i>
      </div>

      <div class="menu-items" [ngClass]="{'expanded': gymMenuOpen || collapsed}">
        <a class="menu-item" routerLink="company/unified-add" routerLinkActive="active">
          <i class="fas fa-plus-square"></i>
          <span *ngIf="!collapsed">Yeni Salon Ekle</span>
        </a>
        <a class="menu-item" routerLink="companyuserdetails" routerLinkActive="active">
          <i class="fas fa-id-card"></i>
          <span *ngIf="!collapsed">Salon Sahipleri</span>
        </a>
        <a class="menu-item" routerLink="license-transactions" routerLinkActive="active">
          <i class="fas fa-receipt"></i>
          <span *ngIf="!collapsed">Satış Raporları</span>
        </a>
        <a class="menu-item" routerLink="deleted-companies" routerLinkActive="active">
          <i class="fas fa-trash-restore"></i>
          <span *ngIf="!collapsed">Silinen Salonlar</span>
        </a>
       
        
      </div>
    </div>

    <!-- License Management Section -->
    <div class="menu-section" *ngIf="isOwner">
      <div class="menu-header" (click)="toggleMenuSection('license')">
        <div class="menu-icon">
          <i class="fas fa-certificate"></i>
        </div>
        <span class="menu-title" *ngIf="!collapsed">Lisans Yönetimi</span>
        <i class="fas fa-chevron-down menu-arrow" *ngIf="!collapsed" [ngClass]="{'rotated': licenseMenuOpen}"></i>
      </div>

      <div class="menu-items" [ngClass]="{'expanded': licenseMenuOpen || collapsed}">
        <a class="menu-item" routerLink="license-packages-add" routerLinkActive="active">
          <i class="fas fa-box-open"></i>
          <span *ngIf="!collapsed">Lisans Paketleri</span>
        </a>
        <a class="menu-item" routerLink="user-licenses" routerLinkActive="active">
          <i class="fas fa-user-tag"></i>
          <span *ngIf="!collapsed">Aktif Lisanslar</span>
        </a>
        <a class="menu-item" routerLink="expired-licenses" routerLinkActive="active">
          <i class="fas fa-exclamation-triangle"></i>
          <span *ngIf="!collapsed">Lisansı Dolan Üyeler</span>
        </a>
      </div>
    </div>

    <!-- System Management Section -->
    <div class="menu-section" *ngIf="isOwner">
      <div class="menu-header" (click)="toggleMenuSection('system')">
        <div class="menu-icon">
          <i class="fas fa-cogs"></i>
        </div>
        <span class="menu-title" *ngIf="!collapsed">Sistem Yönetimi</span>
        <i class="fas fa-chevron-down menu-arrow" *ngIf="!collapsed" [ngClass]="{'rotated': systemMenuOpen}"></i>
      </div>

      <div class="menu-items" [ngClass]="{'expanded': systemMenuOpen || collapsed}">
        <a class="menu-item" routerLink="roles" routerLinkActive="active">
          <i class="fas fa-user-shield"></i>
          <span *ngIf="!collapsed">Rol Yönetimi</span>
        </a>
        <a class="menu-item" routerLink="user-roles" routerLinkActive="active">
          <i class="fas fa-users-cog"></i>
          <span *ngIf="!collapsed">Kullanıcı Rolleri</span>
        </a>
        <a class="menu-item" routerLink="devices" routerLinkActive="active">
          <i class="fas fa-mobile-alt"></i>
          <span *ngIf="!collapsed">Aktif Cihazlar</span>
        </a>
        <a class="menu-item" routerLink="cache-admin" routerLinkActive="active">
          <i class="fas fa-database"></i>
          <span *ngIf="!collapsed">Cache Yönetimi</span>
        </a>
        <a class="menu-item" routerLink="rate-limit-test" routerLinkActive="active">
          <i class="fas fa-shield-alt"></i>
          <span *ngIf="!collapsed">Güvenlik Testi</span>
        </a>
         <a class="menu-item" routerLink="company-selector" routerLinkActive="active">
          <i class="fas fa-exchange-alt"></i>
          <span *ngIf="!collapsed">Salon Değiştir</span>
        </a>
      </div>
    </div>
  </div>

  <!-- Sidebar Footer -->
  <div class="sidebar-footer">
    <div class="user-profile-container" *ngIf="isAuthenticated()">
      <a class="profile-btn" routerLink="/profile">
        <!-- Profile Image or Icon -->
        <div class="profile-image-container">
          <img 
            *ngIf="hasProfileImage && profileImageUrl" 
            [src]="profileImageUrl" 
            alt="Profil Fotoğrafı"
            class="profile-image"
            (error)="onProfileImageError()"
            (load)="onProfileImageLoad()">
          <i 
            *ngIf="!hasProfileImage || !profileImageUrl" 
            class="fas fa-user-circle profile-icon"></i>
        </div>
        <span *ngIf="!collapsed">Profilim</span>
      </a>
    </div>

<div class="theme-toggle-container">
  <button class="theme-toggle-btn" (click)="onToggleDarkMode()" *ngIf="!collapsed">
    <i class="fas" [ngClass]="isDarkMode ? 'fa-sun' : 'fa-moon'"></i>
    <span>{{ isDarkMode ? 'Aydınlık Mod' : 'Karanlık Mod' }}</span>
  </button>
  <button class="theme-toggle-btn icon-only" (click)="onToggleDarkMode()" *ngIf="collapsed">
    <i class="fas" [ngClass]="isDarkMode ? 'fa-sun' : 'fa-moon'"></i>
  </button>
</div>

    <a class="logout-btn" href="#" (click)="logout($event)" *ngIf="isAuthenticated()">
      <i class="fas fa-sign-out-alt"></i>
      <span *ngIf="!collapsed">Çıkış Yap</span>
    </a>
  </div>
</div>
